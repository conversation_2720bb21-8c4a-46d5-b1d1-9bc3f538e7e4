{"name": "vue3.0", "version": "0.0.0", "scripts": {"dev": "vite --host 0.0.0.0", "build": "run-p  build-only", "preview": "vite preview", "postbuild": "node scripts/copy-vditor-assets.cjs", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "precommit": "eslint src/**/*", "prepare": "husky install"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@endlessorigin/select_to_ask": "0.2.7-alpha.2", "@floating-ui/dom": "^1.6.8", "@floating-ui/vue": "^1.1.2", "@highlightjs/vue-plugin": "^2.1.0", "@iktakahiro/markdown-it-katex": "^4.0.1", "@rollup/plugin-commonjs": "^26.0.1", "@types/he": "^1.2.3", "@types/url-parse": "^1.4.11", "axios": "^1.7.2", "cos-js-sdk-v5": "^1.8.4", "dom-serializer": "^2.0.0", "domhandler": "^5.0.3", "element-plus": "^2.8.3", "entities": "^6.0.0", "express": "^5.1.0", "he": "^1.2.0", "highlight.js": "^11.11.1", "htmlparser2": "^10.0.0", "katex": "^0.16.22", "less": "^4.2.0", "less-loader": "^12.2.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "mitt": "^3.0.1", "parse5": "^7.2.0", "pinia": "^2.2.4", "pinia-plugin-persist": "^1.0.0", "pinia-plugin-persistedstate": "4.0.0-alpha.4", "url-parse": "^1.5.10", "uuid": "^11.0.2", "vditor": "^3.11.0", "video.js": "^8.17.1", "vite-plugin-mkcert": "^1.17.7", "vue": "^3.4.29", "vue-router": "^4.4.0"}, "devDependencies": {"@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0", "@rushstack/eslint-patch": "^1.3.2", "@tsconfig/node18": "^18.2.0", "@types/lodash-es": "^4.17.12", "@types/node": "^18.18.9", "@vitejs/plugin-vue": "^4.6.2", "@vue/compiler-sfc": "^3.0.4", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.4.0", "code-inspector-plugin": "^0.20.10", "eslint": "^8.46.0", "eslint-plugin-vue": "^9.16.1", "husky": "^8.0.3", "less": "^4.2.0", "lint-staged": "^14.0.1", "npm-run-all": "^4.1.5", "prettier": "^3.0.0", "rollup-plugin-visualizer": "^5.9.2", "terser": "^5.19.4", "typescript": "~5.1.6", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.5.3", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vue-tsc": "^1.8.8"}, "engines": {"node": "v19.0.1"}}