<script setup lang="ts">
import CmpButton from "@/components/CmpButton.vue"
import HoverEditor from "@/components/HoverEditor.vue"
import DomainDialog from "@/view/domain/components/DomainDialog.vue"
import { Angle, Domain } from "@/utils/type"
import {
  getDomainDetail<PERSON>pi,
  getParentAngle<PERSON>pi,
  addAngleApi,
  deleteAngle<PERSON>pi,
  addDomainA<PERSON>,
  copyDomainApi,
  removeDomainApi,
  selectAngle<PERSON><PERSON>,

} from "@/apis/path/domain"
import { useRoute } from "vue-router"
import { onMounted, onUnmounted, ref, watch } from "vue"
import {
  editTitleApi,
  editDescriptionApi,
  editParentApi,
  editParentAngleApi,
} from "@/apis/path/domain"
import type {
  params2EditTitle,
  params2EditDescription,
  params2EditParent,
  params2EditParentAngle,
  params2EditParentArea,
} from "@/apis/path/domain"
import { ElMessage, ElMessageBox } from "element-plus"
import { Event } from "@/types/event"

import router from "@/router"
import { emitter } from "@/utils/emitter"

// 希望我这样写你不会生气
const visible1 = ref(true) // 领域名称
const visible2 = ref(true) // 父领域
const visible3 = ref(true) // 领域描述
const visible4 = ref(true) // 是否有标签
const visible5 = ref(true) // 所属父领域角度
const visible6 = ref(true) // 本领域角度集
const visible7 = ref(true) // 显示当前角度
const visible8 = ref(true) // 增加子领域

const resetAll = () => {
  visible1.value = true
  visible2.value = true
  visible3.value = true
  visible4.value = true
  visible5.value = true
  visible6.value = true
  visible7.value = true
  visible8.value = true
  curDomain.value = blankDomain
  curAngle.value = blankAngle
}

// 处理刷新树
const handleRefreshTree = () => {
  emitter.emit(Event.TREE_REFRESH, true)
}

const dialogRef = ref()
const route = useRoute()
const blankDomain: Domain = {
  areaCode: "",
  title: "",
  parentTitle: "",
  parentId: -1,
  description: "",
  isTag: 0,
  angle: "",
  angleList: [],
  areaList: [],
  parentNode: {
    isEditKlg: false,
    isEditArea: false,
  },
}
const blankAngle: Angle = {
  angleTitle: "",
  oid: 0,
  list: [],
}
const editDomain = ref({
  title: "",
  description: "",
  angleTitle: "",
  curAngleTitle: "",
  parentId: 0,
  parentAngleTitle: "",
  parentAngleId: "",
})
const curDomain = ref(blankDomain)
const curAngle = ref(blankAngle)
const curAreaCode = ref()
const parentAngleOptions = ref<Angle[]>([])
const canShow = ref(false) // 标识当前领域是否有访问权限

// 获取领域详情
const getDomainDetail = (data?: any) => {
  if (data) {
    canShow.value = data.isEditArea
    curAreaCode.value = data.areaCode
    if (data.parentNode) {
      curDomain.value.parentNode = data.parentNode
    }
  }
  if (canShow.value) {
    getDomainDetailApi(curAreaCode.value).then((res) => {
      if (res.success) {
        resetAll()
        curDomain.value.title = res.data.list.title
        curDomain.value.parentTitle = res.data.list.parentTitle
        curDomain.value.parentAreaCode = res.data.list.parentAreaCode
        curDomain.value.description = res.data.list.description
        curDomain.value.isTag = res.data.list.isTag
        curDomain.value.angle = res.data.list.angle
        curDomain.value.angleList = res.data.list.angleList
        curDomain.value.areaList = res.data.list.areaList
        editDomain.value.title = curDomain.value.title
        editDomain.value.description = curDomain.value.description
          ? curDomain.value.description
          : ""
        curDomain.value.areaCode = curAreaCode.value
        if (curDomain.value.areaList) {
          const oid = curDomain.value.angleList?.find((item)=>item.activate == 1)?.oid
          if(oid) {
            curAngle.value = curDomain.value.areaList?.find((item)=>item.oid === oid)
            emitter.emit(Event.TREE_FILTER, {areaCode: curAreaCode.value, angleId: oid})
          } else {
            curAngle.value = curDomain.value.areaList[0]
            emitter.emit(Event.TREE_FILTER, {areaCode: curAreaCode.value, angleId: curDomain.value.areaList[0].oid})
          }
          editDomain.value.curAngleTitle = curAngle.value.angleTitle
        }
      }
    })
  } else {
  }
}
// 获取父领域角度列表
const getParentAngleList = () => {
  if (curAreaCode.value) {
    getParentAngleApi(curAreaCode.value).then((res) => {
      if (res.success) {
        parentAngleOptions.value = res.data.list
      }
    })
  }
}
// 处理选择父领域角度
const handleSelectParentAngle = (item: any) => {
  editDomain.value.parentId = item.oid
  editDomain.value.parentAngleTitle = item.angleTitle
}

// 处理编辑领域名称
const handleEditTitle = () => {
  const params: params2EditTitle = {
    areaCode: curDomain.value.areaCode,
    title: editDomain.value.title,
    parentAreaCode: curDomain.value.parentAreaCode
      ? curDomain.value.parentAreaCode
      : "",
  }
  editTitleApi(params).then((res) => {
    if (res.success) {
      ElMessage.success("修改成功")
      visible1.value = !visible1.value
      curDomain.value.title = editDomain.value.title
      handleEditLabel()
    } else {
      ElMessage.warning(res.message)
    }
  })
}
// 处理编辑父领域
const handleEditParentNode = (data: any) => {
  const params: params2EditParent = {
    areaCode: curDomain.value.areaCode,
    parentAreaCode: data[0],
    // parentId: data[1],
  }
  editParentApi(params).then((res) => {
    if (res.success) {
      handleRefreshTree()
      ElMessage.success("修改成功")
      getDomainDetail()
    } else {
      ElMessage.error(res.message)
    }
  })
}
// 处理编辑领域描述
const handleEditDescription = () => {
  const params: params2EditDescription = {
    areaCode: curDomain.value.areaCode,
    description: editDomain.value.description,
  }
  editDescriptionApi(params).then((res) => {
    if (res.success) {
      ElMessage.success("修改成功")
      curDomain.value.description = editDomain.value.description
      visible3.value = !visible3.value
    } else {
      ElMessage.error(res.message)
    }
  })
}

// 处理编辑所属父领域角度
const handleEditParentAngle = () => {
  const params: params2EditParentAngle = {
    areaCode: curDomain.value.areaCode,
    angleId: editDomain.value.parentId,
  }
  editParentAngleApi(params).then((res) => {
    if (res.success) {
      ElMessage.success("修改成功")
      visible5.value = !visible5.value
    } else {
      ElMessage.error(res.message)
    }
  })
}
// 处理增加角度
const handleAddAngle = () => {
  addAngleApi(curDomain.value.areaCode, editDomain.value.angleTitle).then(
    (res) => {
      if (res.success) {
        ElMessage.success("添加成功")
        getDomainDetail()
        visible6.value = !visible6.value
        editDomain.value.angleTitle = ""
      } else {
        ElMessage.error(res.message)
      }
    }
  )
}
// 处理删除角度
const deleteAngle = (item: any) => {
  deleteAngleApi(curAreaCode.value, item.oid).then((res)=>{
    if(res.success) {
      ElMessage.success("删除成功")
      if (curDomain.value.angleList) {
        const index = curDomain.value.angleList.findIndex(angle => angle.oid === item.oid);
        if (index !== -1) {
          curDomain.value.angleList.splice(index, 1);
        }
      }
    }else{
      ElMessage.warning(res.message)
    }
  })
}
// 处理增加领域
const handleAddDomain = () => {
  if (curAreaCode.value && curAngle.value.oid) {
    addDomainApi(curAreaCode.value, curAngle.value.oid.toString()).then(
      (res) => {
        if (res.success) {
          handleRefreshTree()
          getDomainDetail()
          ElMessage.success("添加成功")
        }
      }
    )
  }
}
// 处理复制领域
const handleCopyDomain = () => {
  ElMessageBox.confirm("确认复制该领域吗", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    if (curAreaCode) {
      copyDomainApi(curAreaCode.value).then((res) => {
        if (res.success) {
          handleRefreshTree()
          ElMessage.success("复制成功")
        }
      })
    }
  })
}
// 处理删除领域
const handleRemoveDomain = () => {
  ElMessageBox.confirm("确认删除该领域吗", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      if (curAreaCode) {
        removeDomainApi(curAreaCode.value).then((res) => {
          if (res.success) {
            handleRefreshTree()
            handlePush()
            ElMessage.success("删除成功")
          } else {
            ElMessage.warning(res.message)
          }
        })
      }
    })
    .catch((err) => {
      console.log("err", err)
    })
}
// 处理打开dialog
const handleDialog = (mode: number, data: any) => {
  dialogRef.value.showDialog(mode, data)
}
// 处理编辑领域名称
const handleEditLabel = () => {
  router.replace({ query: { ...route.query, label: editDomain.value.title } })
  handleRefreshTree()
}
// 处理返回父级
const handlePush = () => {
  if (curDomain.value.parentNode.isEditArea) {
    router.replace({
      query: {
        ...route.query,
        areaCode: curDomain.value.parentAreaCode,
        label: curDomain.value.parentTitle,
      },
    })
    emitter.emit(Event.EMIT_NODE_CLICK, curDomain.value.parentAreaCode)
  } else {
    ElMessage.warning("父领域无访问权限")
  }
}
// 处理点击查看节点
const handleEmitCheckNode = (data: any) => {
  getDomainDetail(data)
  getParentAngleList()
}
// 处理角度切换
const handleChangeAngle = (item: any) => {
  selectAngleApi(curDomain.value.areaCode, item.oid).then(res=>{
    if(res.success) {
      curAngle.value = item
      visible7.value = !visible7.value
      // handleRefreshTree()
      emitter.emit(Event.TREE_FILTER, {areaCode: curDomain.value.areaCode, angleId: item.oid})
      ElMessage.success("切换成功")
    } else {
      ElMessage.warning(res.message)
    }
  })
}

onMounted(() => {
  handleRefreshTree()
  emitter.on(Event.CHECK_TREE_NODE, handleEmitCheckNode)
})
onUnmounted(() => {
  emitter.off(Event.CHECK_TREE_NODE, handleEmitCheckNode)
})
</script>
<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <span class="header-title" style="word-break: break-all">{{
        route.query.label
      }}</span>
      <span class="header-return" @click="handlePush">返回父级</span>
    </div>
    <div class="line"></div>
    <div v-if="!canShow" class="background-image-container">
      <img
        src="/static/area-glass.png"
        draggable="false"
        style="user-select: none"
      />
      <span class="nopermission-tip">暂无查看权限</span>
    </div>
    <div class="main-wrapper" v-else style="margin-top: 10px">
      <div class="toolbar">
        <CmpButton
          type="primary"
          style="margin-right: 10px"
          @click="handleCopyDomain"
          v-if="curDomain.parentNode.isEditArea"
          >复制领域</CmpButton
        >
        <CmpButton
          type="primary"
          style="margin-right: 10px"
          @click="handleDialog(2, curDomain)"
          >添加依赖</CmpButton
        >
        <CmpButton
          type="info"
          style="margin-right: 10px"
          @click="handleRemoveDomain"
          >删除领域</CmpButton
        >
      </div>
      <div class="form-container">
        <el-form class="main-form">
          <el-form-item label="领域名称">
            <hover-editor
              v-if="visible1"
              :label="`修改领域名称`"
              @edit="visible1 = !visible1"
            >
              <span style="word-break: break-all">{{ curDomain.title }}</span>
            </hover-editor>
            <span class="input-box" v-else>
              <el-input v-model="editDomain.title"></el-input>
              <CmpButton
                type="primary"
                class="input-btn"
                @click.prevent="handleEditTitle"
                >保存</CmpButton
              >
              <CmpButton
                type="info"
                class="input-btn"
                @click.prevent="
                  visible1 = !visible1;
                  editDomain.title = curDomain.title
                "
                >取消</CmpButton
              >
            </span>
          </el-form-item>
          <div class="line"></div>
          <el-form-item label="父领域">
            <hover-editor
              :label="`修改所属父领域`"
              @edit="handleDialog(0, curDomain)"
            >
              <span style="word-break: break-all">{{
                curDomain.parentTitle ? curDomain.parentTitle : "-"
              }}</span>
            </hover-editor>
          </el-form-item>
          <div class="line"></div>
          <el-form-item label="领域描述">
            <hover-editor
              v-if="visible3"
              :label="`修改领域描述`"
              @edit="visible3 = !visible3"
            >
              <span
                v-if="curDomain.description"
                class="html-content"
                v-html="curDomain.description"
              />
              <span v-else>暂无</span>
            </hover-editor>
            <span class="input-box" style="width: 100%" v-else>
              <el-input
                resize="none"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
                style="width: 100%"
                placeholder="请输入领域描述"
                v-model="editDomain.description"
              ></el-input>
              <CmpButton
                type="primary"
                class="input-btn"
                @click.prevent="handleEditDescription"
                >保存</CmpButton
              >
              <CmpButton
                type="info"
                class="input-btn"
                @click.prevent="
                  visible3 = !visible3;
                  editDomain.description = curDomain.description
                    ? curDomain.description
                    : ''
                "
                >取消</CmpButton
              >
            </span>
          </el-form-item>
          <div class="line"></div>
          <el-form-item label="是否为标签">
            {{ curDomain.isTag ? "是" : "否" }}
          </el-form-item>
          <div class="line"></div>
          <el-form-item label="所属父领域角度">
            <hover-editor
              v-if="visible5"
              :label="`切换所属父领域角度`"
              @edit="visible5 = !visible5"
            >
              {{ curDomain.angle ? curDomain.angle : "暂无" }}
            </hover-editor>
            <span class="input-box" style="width: 100%" v-else>
              <el-select v-model="curDomain.angle" popper-class="primary">
                <el-option
                  v-for="item in parentAngleOptions"
                  :key="item.oid"
                  :label="item.angleTitle"
                  :value="item.angleTitle"
                  @click="handleSelectParentAngle(item)"
                />
              </el-select>
              <CmpButton
                type="primary"
                class="input-btn"
                @click.prevent="handleEditParentAngle"
                >保存</CmpButton
              >
              <CmpButton
                type="info"
                class="input-btn"
                @click.prevent="visible5 = !visible5"
                >取消</CmpButton
              >
            </span>
          </el-form-item>
          <div class="line"></div>
          <el-form-item label="本领域角度集">
            <div class="angle-block">
              <span
                v-if="visible6"
                class="add-angle"
                @click="visible6 = !visible6"
              >
                <img src="@/assets/image/add.svg" />
                <span class="add-angle-text" style="margin-left: 5px"
                  >增加本领域角度</span
                >
              </span>
              <span v-else class="input-box">
                <el-input
                  style="width: 100%"
                  v-model="editDomain.angleTitle"
                  placeholder="请输入角度名称"
                ></el-input>
                <CmpButton
                  type="primary"
                  class="input-btn"
                  @click.prevent="handleAddAngle"
                  >保存</CmpButton
                >
                <CmpButton
                  type="info"
                  class="input-btn"
                  @click.prevent="visible6 = !visible6"
                  >取消</CmpButton
                >
              </span>
              <span v-for="item in curDomain.angleList" class="angle-item">
                <span>{{ item.angleTitle }}</span>
                <span class="close-btn" @click="deleteAngle(item)"><el-icon><Close /></el-icon></span>
              </span>
            </div>
          </el-form-item>
          <div class="line"></div>
          <el-form-item label="包含子领域">
            <div class="domain-container">
              <div class="cur-angle-block">
                <span style="margin-right: 10px">当前显示角度:</span>
                <hover-editor
                  v-if="visible7"
                  :label="`切换当前显示角度`"
                  @edit="visible7 = !visible7"
                  style="
                    background-color: #fafafa;
                    padding: 0px 7px;
                    min-width: 400px;
                  "
                >
                  {{ curAngle.angleTitle }}
                </hover-editor>
                <span class="input-box" style="width: 80%" v-else>
                  <el-select
                    v-model="editDomain.curAngleTitle"
                    popper-class="primary"
                  >
                    <el-option
                      v-for="(item, index) in curDomain.areaList"
                      :key="item.oid"
                      :label="item.angleTitle"
                      :value="item.oid"
                      @click="
                        handleChangeAngle(item)
                      "
                    />
                  </el-select>
                  <CmpButton
                    type="info"
                    class="input-btn"
                    @click.prevent="visible7 = !visible7"
                    >取消</CmpButton
                  >
                </span>
              </div>
              <div class="area-btn-group" v-if="curAngle.oid !== 0">
                <span
                  class="area-btn"
                  @click="handleAddDomain"
                  style="margin-right: 40px"
                >
                  <img src="@/assets/image/add.svg" />
                  <span style="margin-left: 5px">增加子领域</span>
                </span>
                <span class="area-btn" style="margin-right: 40px" @click="handleDialog(1, curDomain)">
                  <img src="@/assets/image/domain/u153.svg" />
                  <span style="margin-left: 5px">查看子领域关系</span>
                </span>
                <span class="area-btn"  @click="handleDialog(3, {domain: curDomain, angle: curAngle})">
                  <el-icon style="transform: rotate(90deg);"><Switch /></el-icon>
                  <span style="margin-left: 5px">子领域排序</span>
                </span>
              </div>
              <ul class="area-list">
                <li
                  v-for="(item) in curAngle.list"
                  :key="item.areaCode"
                >
                  {{ item.title }}
                </li>
              </ul>
            </div>
          </el-form-item>
          <div class="line"></div>
        </el-form>
      </div>
    </div>
  </div>
  <DomainDialog
    ref="dialogRef"
    @edit-p-node="handleEditParentNode"
  ></DomainDialog>
</template>
<style scoped>
.background-image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url("/static/area-glass.png") no-repeat;
  background-size: 100%;
  /* width: 1200px;
  height: 83vh; */
  /* background-size: cover; */
  /* background-position: center; */
  user-select: none;
  .nopermission-tip {
    position: absolute;
    top: 300px;
    border: 1px solid var(--color-boxborder);
    background-color: white;
    padding: 10px;
    border-radius: 3px;
    font-size: 18px;
    font-weight: 800;
    color: var(--color-black);
  }
}
@media screen and (max-width: 1000px) {
  :deep(.wrapper) {
    background-color: blue;
    width: 500px;
  }
}
.wrapper {
  width: 62.5vw;
  background-color: white;
  min-height: 750px;
  word-break: break-all;
  .header-wrapper {
    width: 100%;
    display: flex;
    justify-content: space-between;

    .header-title {
      margin: 10px 30px;
      font-weight: 600;
      color: var(--color-black);
    }
    .header-return {
      color: var(--color-primary);
      font-size: 12px;
      margin: 10px 30px;
      display: flex;
      align-items: center;

      &:hover {
        cursor: pointer;
        font-weight: 600;
      }
    }
  }
  .main-wrapper {
    padding: 10px;
    margin: 0 70px;
    .toolbar {
    }
    .form-container {
      margin-top: 20px;
      .main-form {
        color: var(--color-black);
        :deep(.el-form-item__label) {
          color: var(--color-black);
          font-weight: 600;
        }
        .input-box {
          display: flex;
          align-items: center;
          .input-btn {
            width: 50px;
            height: 28px;
            margin-left: 5px;
          }
        }
        .angle-block {
          width: 100%;
          display: flex;
          flex-direction: column;
          .add-angle {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 12px;
            .add-angle-text {
              color: var(--color-primary);
            }
          }
          .angle-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0px 10px;
            border-radius: 3px;
            position: relative;
            transition: background-color 0.3s ease;
          }

          .close-btn {
            cursor: pointer;
            opacity: 0; 
            transition: opacity 0.3s ease; 
            position: absolute; 
            top: 60%;
            right: 10px;
            transform: translateY(-50%); 
          }

          .angle-item:hover .close-btn {
            opacity: 1; 
          }

          .angle-item:hover {
            background-color: #FAFAFA;
          }
        }
        .domain-container {
          width: 100%;
          .cur-angle-block {
            width: 100%;
            display: flex;
            align-items: center;
          }
          .area-btn-group {
            width: 100%;
            display: flex;
            .area-btn {
              display: flex;
              align-items: center;
              color: var(--color-primary);
              font-size: 12px;
              cursor: pointer;
            }
          }
          .area-list {
            width: 100%;
            display: flex;
            flex-direction: column;
          }
        }
        .html-content {
          width: 80%;
          :deep(p) {
            margin: 0;
          }
        }
      }
    }
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
</style>
<style>
.primary {
  --el-color-primary: var(--color-primary);
}
</style>
