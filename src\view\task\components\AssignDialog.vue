<script setup lang="ts">
import CmpButton from '@/components/CmpButton.vue';
import { computed, ref } from 'vue';
import { getHandlerListApi, assignHandlerApi, params2AssignHandler } from '@/apis/path/task';
import { ElMessage } from 'element-plus';
const dialogVisible = ref(); // dialog可见
const titleList = ['批量分配', '任务分配'];
const titleType = ref(-1); // -1: default || 0: 批量分配 || 1: 任务分配
const curHandler = ref({
  handlerId: '',
  handlerName: ''
}); // 当前处理人
const handlerList = ref(); // 处理人列表
const curAreaCode = ref(''); // 当前areacode => 复数拼接 "areacode1,areacode2"
const curTaskCode = ref(''); // 当前taskcode => 复数拼接 "taskId1,taskId2"
const curAnswerId = ref(''); // 当前answerId => 复数拼接 "answerId1,answerId2"
const data = ref(); // 传入数据
const dataLength = ref(-1);
const tagVisible = computed(() => curHandler.value.handlerName !== ''); // 判断是否可视tag
const emits = defineEmits(['refresh']);
const loading = ref();

// 展示dialog
const showDialog = (tasks: any | [], type: number) => {
  // 进入时初始化
  dataLength.value = -1;
  handleCancelHandler();
  if (tasks.handlerId) {
    curHandler.value.handlerId = tasks.handlerId;
    curHandler.value.handlerName = tasks.handlerName;
  }

  dialogVisible.value = true;
  titleType.value = type;
  data.value = tasks;

  // 处理字符串 复数用','连接
  if (type === 0) {
    curAreaCode.value = tasks.map((task) => task.areaCode).join(',');
    curTaskCode.value = tasks.map((task) => task.oid).join(',');
    curAnswerId.value = tasks
      .map((task) => task.answerId)
      .filter((answerId, index, self) => self.indexOf(answerId) === index)
      .join(',');
    dataLength.value = data.value.length;
  } else if (type === 1) {
    curAreaCode.value = tasks.areaCode;
    curTaskCode.value = tasks.oid;
    curAnswerId.value = tasks.answerId;
  }
};
// 筛选处理人
const remoteHandler = (key: string) => {
  loading.value = true;
  getHandlerListApi(key, curAreaCode.value).then((res) => {
    if (res.success) {
      handlerList.value = res.data.list;
      loading.value = false;
    }
  });
};
// 处理选择负责人
const handleSelectHandler = (item: any) => {
  curHandler.value.handlerId = item.userId;
  curHandler.value.handlerName = item.showName;
};
// 处理取消负责人
const handleCancelHandler = () => {
  curHandler.value.handlerName = '';
  curHandler.value.handlerId = '';
};
// 处理关闭
const handleClose = () => {
  dialogVisible.value = false;
};
// 处理提交
const handleSubmit = () => {
  const params: params2AssignHandler = {
    taskId: curTaskCode.value,
    userId: curHandler.value.handlerId,
    answerId: curAnswerId.value
  };
  assignHandlerApi(params).then((res) => {
    if (res.success) {
      ElMessage.success('分配成功');
      handleClose();
      emits('refresh', true);
    }
  });
};
defineExpose({
  showDialog
});
</script>
<template>
  <el-dialog v-model="dialogVisible" width="40%">
    <template #header>
      <span>{{ titleList[titleType] }} </span>
    </template>
    <div class="line"></div>
    <el-form class="main-wrapper">
      <el-form-item v-if="titleType === 0">
        <span>您选择了{{ dataLength }}个任务，需要分配给负责人。</span>
      </el-form-item>
      <el-form-item v-else-if="titleType === 1">
        <div class="info-content">
          <span class="name" v-html="data.klgName"> </span>
          <span class="domain">
            {{ data.areaTitle }}
          </span>
          <span class="foot-bar">
            <span class="creator">
              {{ data.creatorName }}
            </span>
            <span class="time">
              {{ data.createTime }}
            </span>
          </span>
        </div>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="curHandler.handlerName"
          filterable
          remote
          reserve-keyword
          placeholder="请输入负责人名称"
          :remote-method="remoteHandler"
          :loading="loading"
          :remote-show-suffix="true"
          suffix-icon="Search"
        >
          <el-option
            v-for="item in handlerList"
            :key="item.userId"
            :label="item.showName"
            :value="item.showName"
            @click="handleSelectHandler(item)"
            class="option"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <span style="color: var(--color-black)">选择负责人:</span>
        <span>
          <el-tag
            class="handler-tag"
            v-if="tagVisible"
            closable
            :disable-transitions="true"
            @close="handleCancelHandler()"
          >
            {{ curHandler.handlerName }}
          </el-tag>
        </span>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <CmpButton class="footer-btn normal" @click="handleClose">返回</CmpButton>
        <CmpButton class="footer-btn" type="primary" @click="handleSubmit">提交</CmpButton>
      </div>
    </template>
  </el-dialog>
</template>
<style scoped>
.main-wrapper {
  min-height: 400px;
  --el-color-primary: var(--color-primary);
  .info-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: var(--color-back-header);
    color: var(--color-black);
    padding: 10px;
    border-radius: 5px;
    .name {
      font-weight: 600;
    }
    .domain {
      font-weight: 400;
    }
    .foot-bar {
      color: var(--color-deep);
      .creator {
        margin-right: 20px;
      }
      .time {
      }
    }
  }

  .handler-tag {
    background-color: var(--color-group-background);
    color: #0d2040;
    border: none;
    padding: 5px 10px;
    margin-left: 10px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  --el-color-primary: var(--color-primary);
  .footer-btn {
    width: 90px;
    height: 25px;
    border-radius: 3px;
    border: 1px solid var(--color-primary);
  }
  .normal {
    margin-right: 20px;
    background-color: white;
    &:hover {
      background-color: var(--color-second);
    }
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
  margin-bottom: 5px;
}
.option {
  --el-color-primary: var(--color-primary);
}
:deep(.el-select__caret.is-reverse) {
  transform: rotate(0deg);
}

:deep(.el-tag .el-tag__close) {
  color: #0d2040;
}
:deep(.el-tag .el-tag__close:hover) {
  background-color: transparent;
}
</style>
