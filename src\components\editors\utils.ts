// 检查文件大小是否超过限制
export const isFileSizeExceeded = (
  file: File,
  maxSize: number = 2 * 1024 * 1024
): boolean => {
  const result = file.size > maxSize;
  if (result) {
    console.log(
      `文件大小检查: ${file.name}, 大小: ${file.size}字节, 超过2MB限制`
    );
  }
  return result;
};
export const safeFilename = (name: string): string => {
  return (
    name
      // 只保留英文字母、数字、中文和点号
      .replace(/[^(a-zA-Z0-9\u4e00-\u9fa5\.)]/g, "")
      // 移除特殊字符(?/\:|<>*[]()${}@~)
      .replace(/[\?\\/:|<>\*\[\]\(\)\$%\{\}@~]/g, "")
      // 移除空白字符
      .replace("/\\s/g", "")
  );
};

