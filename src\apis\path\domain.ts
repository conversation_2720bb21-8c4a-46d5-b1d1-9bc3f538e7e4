import { http } from "@/apis"
import type { APIResponse, AreaDep } from "@/utils/type"
export interface params2EditTitle {
  parentAreaCode: string,
  areaCode: string,
  title: string,
}
export interface params2EditParent {
  areaCode: string,
  parentAreaCode: string,
  // parentId: number
}
export interface params2EditDescription {
  areaCode: string,
  description: string,
}
export interface params2EditTag {
  areaCode: string,
  isTag: number,
}
export interface params2EditParentArea {
  areaCode: string,
  parentAreaCode: string,
  parentId: number,
}
export interface params2EditParentAngle {
  areaCode: string,
  angleId: number
}
export interface params2EditReferNode {
  areaCode: string,
  editAreaCond: AreaDep[],
}

export interface params2OrderDomain {
  areaCode: string,
  angleId: number,
  sortAreaList: {
    areaCode: string,
    sort: number
  }[]
}

// 拿到全量树
export function getFullTreeApi(): Promise<APIResponse>  {
  return http.request({
    method: "get",
    url: `/area/getFullTree`,
  })
}
// 拿到树
export function getTreeApi(flag: number): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/area/getAreaTree/${flag}`,
  })
}
// 获取领域详情
export function getDomainDetailApi(id: string): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/area/getAreaDetail/${id}`,
  })
}

// 编辑领域名称
export function editTitleApi(params: params2EditTitle): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/area/editTitle`,
    data: params
  })
}
// 编辑领域名称
export function editParentApi(params: params2EditParent): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/area/editParentArea`,
    data: params
  })
}
// 编辑领域描述
export function editDescriptionApi(params: params2EditDescription): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/area/editDescription`,
    data: params
  })
}
// 编辑领域标签
export function editTagApi(params: params2EditTag): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/area/editTag`,
    data: params
  })
}
// 编辑所属父领域角度
export function editParentAngleApi(params: params2EditParentAngle): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/area/editParentAngle`,
    data: params
  })
}
// 获取父角度列表
export function getParentAngleApi(areaCode: string): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/area/getParentAngleList?areaCode=${areaCode}`,
  })
}
// 新增角度
export function addAngleApi(areaCode: string, angleTitle: string): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/area/addAngle?areaCode=${areaCode}&angleTitle=${angleTitle}`
  })
}
// 删除角度
export function deleteAngleApi(areaCode: string, angleId: string): Promise<APIResponse> {
  return http.request({
    method: 'get',
    url: `/area/deleteAngle?areaCode=${areaCode}&angleId=${angleId}`
  })
}
// 选择角度
export function selectAngleApi(areaCode: string, angleId: number): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/area/angle/activate`,
    data: {
      areaCode: areaCode,
      angleId: angleId,
    }
  })
}
// 增加子领域
export function addDomainApi(parentAreaCode: string, angleId: string): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/area/addArea?parentAreaCode=${parentAreaCode}&angleId=${angleId}`
  })
}
// 排序子领域
export function orderDomainApi(params: params2OrderDomain): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/area/peer/sort`,
    data: params
  })
}
// 复制领域
export function copyDomainApi(areaCode: string): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/area/copyArea/${areaCode}`
  })
}
// 删除领域
export function removeDomainApi(areaCode: string): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/area/delete/${areaCode}`
  })
}
// 获取同级依赖
export function getReferNodeApi(areaCode: string): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/same/cond/getDep/${areaCode}`
  })
}
// 添加同级依赖
export function editReferNodeApi(params: params2EditReferNode): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/same/cond/editSameAreaCond`,
    data: params
  })
}