# EditablePreview 组件

一个支持编辑/预览模式切换的输入框组件，特别适用于需要 LaTeX 数学公式渲染的场景。

## 功能特性

- ✅ 编辑/预览模式自动切换
- ✅ 支持 LaTeX 数学公式渲染
- ✅ 自动聚焦和失焦处理
- ✅ 可自定义样式和配置
- ✅ 完整的事件回调支持
- ✅ TypeScript 类型支持

## 基本用法

```vue
<template>
  <EditablePreview
    v-model="content"
    placeholder="请输入内容，支持数学公式"
    width="400px"
    height="35px"
  />
</template>

<script setup>
import { ref } from 'vue'
import EditablePreview from '@/components/EditablePreview.vue'

const content = ref('这是一个数学公式：$E = mc^2$')
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `modelValue` | `string` | `''` | 绑定值 |
| `placeholder` | `string` | `'请输入内容，支持数学公式'` | 占位符文本 |
| `maxlength` | `number` | `256` | 最大输入长度 |
| `width` | `string` | `'100%'` | 组件宽度 |
| `height` | `string` | `'35px'` | 组件高度 |
| `previewHint` | `string` | `'点击编辑，支持latex公式'` | 预览模式提示文字 |
| `showWordLimit` | `boolean` | `true` | 是否显示字数限制 |
| `disabled` | `boolean` | `false` | 是否禁用 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `(value: string)` | 值更新时触发 |
| `focus` | `(event: FocusEvent)` | 获得焦点时触发 |
| `blur` | `(event: FocusEvent)` | 失去焦点时触发 |
| `change` | `(value: string)` | 值改变时触发 |

## 使用场景

### 1. 基础文本输入
```vue
<EditablePreview
  v-model="title"
  placeholder="请输入标题"
  :maxlength="100"
/>
```

### 2. 数学公式输入
```vue
<EditablePreview
  v-model="formula"
  placeholder="请输入数学公式，如：$x^2 + y^2 = r^2$"
  width="500px"
/>
```

### 3. 自定义样式
```vue
<EditablePreview
  v-model="content"
  width="800px"
  height="50px"
  preview-hint="点击此处编辑内容"
  :show-word-limit="false"
/>
```

### 4. 事件处理
```vue
<EditablePreview
  v-model="content"
  @focus="handleFocus"
  @blur="handleBlur"
  @change="handleChange"
/>
```

## 工作原理

1. **编辑模式**：显示 `el-input` 输入框，用户可以输入内容
2. **预览模式**：显示渲染后的内容，支持 LaTeX 公式渲染
3. **自动切换**：
   - 当输入框失焦且有内容时，自动切换到预览模式
   - 当点击预览区域时，自动切换到编辑模式并聚焦
   - 当输入框获得焦点时，确保处于编辑模式

## LaTeX 公式支持

组件使用 `convertMathFormulas` 工具函数来渲染 LaTeX 公式：

- 行内公式：`$E = mc^2$`
- 块级公式：`$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$`

## 样式自定义

组件提供了完整的 CSS 变量支持，可以通过覆盖以下变量来自定义样式：

```css
.editable-preview {
  --el-border-color: #dcdfe6;
  --el-color-primary: #409eff;
  --el-disabled-bg-color: #f5f7fa;
  --el-disabled-text-color: #c0c4cc;
  --el-text-color-placeholder: #a8abb2;
}
```

## 注意事项

1. 确保项目中已正确配置 LaTeX 渲染环境
2. 组件依赖 Element Plus 的 `el-input` 组件
3. 预览模式下的内容使用 `v-html` 渲染，请确保内容安全
4. 组件会自动处理焦点管理，无需手动控制
