<script setup lang="ts">
import ModeTypeSwitcher from '@/components/ModeTypeSwitcher.vue';
import MyFlipper from '@/components/MyFlipper.vue';
import CmpButton from '@/components/CmpButton.vue';
import FilterBar from '@/view/klg/components/FilterBar.vue';
import MulOpDialog from '@/view/klg/components/MulOpDialog.vue';

import {
  hasPreDict,
  klgAuditType,
  klgAuditTypeDict,
  KlgType,
  KlgTypeDict,
  KlgTypeDictPlus,
  MAX_PAGESIZE,
  searchRange,
  sortByDict,
  sortOrderDict,
  proofCondTypeDict
} from '@/utils/constant';
import {
  getKlgListApi,
  getMyKlgListApi,
  copyKlgApi,
  sendBackKlgApi,
  deleteKlgApi,
  submitKlgInTableApi,
  getProofBlockListApi
} from '@/apis/path/klg';
import type { params2GetKlgList } from '@/apis/path/klg';
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { ElMessage, ElTable } from 'element-plus';
import { useRoute } from 'vue-router';
import { findKeyByValue } from '@/utils/func';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';

const curHeaderMode = ref(0); // 0: 我创建的知识 || 1: 领域全部知识
const dialogRef = ref(); // dialog
const tableRef = ref<InstanceType<typeof ElTable>>();
const currentPage = ref(1); // 当前页
const pageSize = MAX_PAGESIZE; // 页大小
const tableData = ref([]); // 表格数据
const total = ref(0); // 总数
const selectionTaskList = ref([]); // 选择任务列表
const expandRows = ref([]); // 当前展开的论证块列表
const canShow = ref(false);
const canEdit = ref(false);
const curData = ref({
  curAreaCode: '',
  curTitle: ''
});
const curParams = ref<params2GetKlgList>({
  current: 0,
  limit: MAX_PAGESIZE,
  areaCode: '',
  keyword: '',
  hasPre: 2,
  name: '', // 作者（获取全部列表时用）
  number: '', // 编号（获取全部列表时用）
  range: 1, // 查询范围 0: all || 1:mine
  sortBy: 0, //
  sortOrder: 0,
  status: '', // 审核状态1待审核0审核通过2审核拒绝（获取全部列表时使用）
  type: '' // 知识类型
});
const route = useRoute();
// 处理转换header
const handleChangeHeader = (newHeaderMode: number) => {
  curHeaderMode.value = newHeaderMode;
  curParams.value.name = '';
  getKlgList();
};
// 获取任务列表
const getKlgList = (data?: any) => {
  if (data) {
    canShow.value = data.isEditKlg;
    canEdit.value = data.isEditArea;
    curParams.value.areaCode = data.areaCode;
    curData.value.curAreaCode = data.areaCode;
  }
  if (canShow.value) {
    curParams.value.current = currentPage.value;
    if (curHeaderMode.value === 0) {
      getMyKlgListApi(curParams.value).then((res) => {
        if (res.success) {
          tableData.value = res.data.list;
          total.value = res.data.total;
          tableData.value.forEach((item) => {
            if (item.sortId === KlgType.Principles) {
              getProofBlockListApi(item.klgCode).then((res) => {
                if (res.success) {
                  item.proofList = res.data.klgProofBlocks;
                }
              });
            }
          });
        } else {
        }
      });
    } else if (curHeaderMode.value === 1) {
      getKlgListApi(curParams.value).then((res) => {
        if (res.success) {
          tableData.value = res.data.list;
          total.value = res.data.total;
        }
      });
    }
  } else {
  }
};

// 处理刷新树
const handleRefreshTree = () => {
  emitter.emit(Event.TREE_REFRESH, true);
};

// 处理任务列表更新
const handleRefreshList = (form?: any) => {
  console.log('form', 3);
  if (form) {
    if (form === 3) {
      tableRef.value?.getSelectionRows().forEach((row) => {
        handleDelete(row);
      });
    }
    curParams.value.keyword = form.klgName;
    curParams.value.type =
      form.klgType && form.klgType !== findKeyByValue(0, KlgTypeDictPlus) ? form.klgType : '';
    curParams.value.status =
      form.klgStatus && form.klgStatus !== findKeyByValue(-1, klgAuditTypeDict)
        ? String(klgAuditTypeDict[form.klgStatus])
        : '';
    curParams.value.name = form.author ? form.author : '';
    curParams.value.range = form.range ? searchRange[form.range] : 1;
    curParams.value.sortBy = form.sortBy ? sortByDict[form.sortBy] : 0;
    curParams.value.sortOrder = form.sortOrder ? sortOrderDict[form.sortOrder] : 0;
    curParams.value.hasPre = form.hasPre ? hasPreDict[form.hasPre] : 2;
  }
  getKlgList();
};
// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize + index + 1;
};
// 处理选择task
const handleSelectionChange = (list: []) => {
  selectionTaskList.value = list;
};
// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getKlgList();
};
// 处理复制
const handleCopy = (row: any) => {
  copyKlgApi(row.klgCode).then((res) => {
    if (res.success) {
      ElMessage.success('复制成功');
      handleRefreshList();
    } else {
      ElMessage.error(res.message);
    }
  });
};
// 处理提交
const handleSubmit = (row: any) => {
  submitKlgInTableApi(row.klgCode).then((res) => {
    if (res.success) {
      ElMessage.success('提交成功');
      handleRefreshList();
    } else {
      ElMessage.error(res.message);
    }
  });
};
// 处理编辑
const handleEdit = (row: any) => {
  sessionStorage.setItem('step', '0');
  window.open(`/editklg?klgCode=${row.klgCode}`, '_self');
};
// 处理查看
const handleCheck = (row: any) => {
  window.open(`/klgdetail?klgCode=${row.klgCode}`, '_self');
};
// 处理关联
const handleLink = (row: any) => {
  window.open(`/linkklg?klgCode=${row.klgCode}`, '_self');
};
// 处理删除
const handleDelete = (row: any) => {
  let idList = [];
  idList.push(row.klgCode);
  deleteKlgApi(idList).then((res) => {
    if (res.success) {
      ElMessage.success('删除成功');
      handleRefreshList();
    } else {
      ElMessage.error(res.message);
    }
  });
};
// 处理撤回
const handleReturn = (row: any) => {
  sendBackKlgApi(row.klgCode).then((res) => {
    if (res.success) {
      ElMessage.success('撤回成功');
      handleRefreshList();
    } else {
      ElMessage.error(res.message);
    }
  });
};
// 处理批量
const handleMulOp = (mode: number, data?: any) => {
  switch (mode) {
    case 2:
      dialogRef.value.showDialog(mode, data);
      // 批量上传
      break;
    case 3:
      // 批量删除
      let list: [] = [];
      let flag = true;
      tableRef.value?.getSelectionRows().forEach((item) => {
        if (!(item.status === klgAuditType.draft || item.status === klgAuditType.withdrawn)) {
          flag = false;
          return;
        }
        // @ts-ignore
        list.push(item.klgCode);
      });
      if (flag) {
        if (list.length > 0) {
          dialogRef.value.showDialog(mode);
        } else {
          ElMessage.warning('请至少选择一条！');
        }
      } else {
        ElMessage.warning('请选择草稿或已撤回状态的知识！');
      }
      break;
    case 4:
      // 批量预览
      if (expandRows.value.length === 0) {
        tableData.value.forEach((item) => {
          expandRows.value.push(item.klgCode);
        });
      } else {
        expandRows.value = [];
      }
      break;
    case 5:
      // 批量添入
      dialogRef.value.showDialog(mode, data);
      break;
    case 6:
      // 批量移入
      dialogRef.value.showDialog(mode, data);
      break;
  }
};
// 处理点击标题
const handleClickTitle = (id: string) => {
  if (curHeaderMode.value === 0) return;
  else {
    window.open(`/klgdetail?klgCode=${id}`);
  }
};

// 处理响应获取数据
const handleEmitCheckNode = (data: any) => {
  if (data.areaCode === curData.value.curAreaCode) return;
  curData.value.curTitle = data.label;
  getKlgList(data);
};

onMounted(() => {
  handleRefreshTree();
  emitter.on(Event.CHECK_TREE_NODE, handleEmitCheckNode);
});
onUnmounted(() => {
  emitter.off(Event.CHECK_TREE_NODE, handleEmitCheckNode);
});
</script>
<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <span class="header-title"> {{ curData.curTitle }} </span>
      <div class="line"></div>
      <div v-if="!canShow" class="background-image-container">
        <img src="/static/klg-glass.png" draggable="false" style="user-select: none" />
        <span class="nopermission-tip">暂无查看权限</span>
      </div>
      <div v-else>
        <div class="header-switcher">
          <mode-type-switcher :mode="curHeaderMode" @changeMode="handleChangeHeader" :length="2">
            <template v-slot:mode0> 我创建的知识 </template>

            <template v-slot:mode1> 领域知识全部 </template>
          </mode-type-switcher>
        </div>
        <div class="header-searcher">
          <el-collapse class="header-collapse">
            <el-collapse-item>
              <template #title>
                <span class="search-btn">搜索</span>
              </template>
              <template #default>
                <div class="search-bar">
                  <filter-bar
                    :disabled="curHeaderMode === 0"
                    @filter="handleRefreshList"
                  ></filter-bar>
                </div>
              </template>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div class="toolbar" v-if="curHeaderMode === 0 && canEdit">
          <CmpButton type="primary" style="margin-right: 10px" @click="handleMulOp(2, curData)"
            >批量上传</CmpButton
          >
          <CmpButton type="primary" style="margin-right: 10px" @click="handleMulOp(3)"
            >批量删除</CmpButton
          >
          <CmpButton type="primary" style="margin-right: 10px" @click="handleMulOp(4)"
            >批量预览</CmpButton
          >
          <CmpButton
            type="primary"
            style="margin-right: 10px"
            @click="handleMulOp(5, curData.curAreaCode)"
            >批量添入
            <el-popover
              placement="bottom"
              title="说明"
              :width="250"
              trigger="hover"
              content="将选中的知识批量添加到当前领域里，原领域不变，使其在本领域下可见。"
            >
              <template #reference>
                <el-icon><WarningFilled /></el-icon>
              </template>
            </el-popover>
          </CmpButton>
          <CmpButton
            type="primary"
            style="margin-right: 10px"
            @click="handleMulOp(6, curData.curAreaCode)"
            >批量移入
            <el-popover
              placement="bottom"
              title="说明"
              :width="250"
              trigger="hover"
              content="将选中的知识批量移动到当前领域里，删除被选时知识领域，使其在本领域下可见。"
            >
              <template #reference>
                <el-icon><WarningFilled /></el-icon>
              </template>
            </el-popover>
          </CmpButton>
        </div>

        <div class="main-wrapper">
          <div class="line"></div>
          <el-table
            ref="tableRef"
            class="table"
            :data="tableData"
            style="width: 100%; overflow-x: hidden"
            empty-text="暂无数据"
            :row-key="(row) => row.klgCode"
            :expand-row-keys="expandRows"
            :row-style="{ overflow: 'hidden' }"
            :cell-style="{
              height: '55px',
              overflow: 'hidden'
            }"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="30" v-if="canEdit && curHeaderMode === 0" />
            <el-table-column type="expand" width="25" v-if="curHeaderMode === 0">
              <template #default="scope">
                <div class="cnt-card">
                  <span class="expand-label"> 知识内容 </span>
                  <div class="content ck-content" v-html="scope.row.cnt"></div>
                </div>
                <div class="cnt-card" v-if="scope.row.sortId === KlgType.Principles">
                  <span class="expand-label"> 论证内容 </span>
                  <span
                    v-for="(block, index) in scope.row.proofList"
                    :key="index"
                    class="proof-block-list"
                  >
                    <span v-for="cond in block.klgProofCondList" class="proof-cond-list">
                      <span class="proof-label">{{
                        findKeyByValue(cond.sort, proofCondTypeDict)
                      }}</span>
                      <span>
                        <span class="ck-content" v-html="cond.cnt"></span>
                      </span>
                    </span>
                    <span class="proof-conclusion">
                      <span class="proof-label">论证结论</span>
                      <span>
                        <span class="ck-content" v-html="block.conclusion"></span>
                      </span>
                    </span>
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              type="index"
              :index="indexMethod"
              label="序号"
              width="60"
              align="center"
            />
            <el-table-column prop="title" label="知识名称" min-width="180">
              <template #default="scoped">
                <el-tooltip
                  placement="top"
                  :content="scoped.row.title"
                  :raw-content="true"
                  :show-after="200"
                  effect="customized"
                >
                  <span
                    class="ellipsis-text-inline"
                    v-html="scoped.row.title"
                    :class="{ 'clickable-title': curHeaderMode === 1 }"
                    @click="handleClickTitle(scoped.row.klgCode)"
                  ></span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="知识类型" width="90" align="center">
              <template #default="scoped">
                {{ scoped.row.sortTitle }}
              </template>
            </el-table-column>
            <el-table-column v-if="curHeaderMode === 1" label="作者" width="100" align="center">
              <template #default="scoped">
                <el-tooltip
                  placement="top"
                  :content="scoped.row.creatorName"
                  :raw-content="true"
                  :show-after="200"
                  effect="customized"
                >
                  <span class="ellipsis-text-inline" v-html="scoped.row.creatorName"></span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="100px" align="center" />
            <el-table-column
              prop="modifiedTime"
              label="最后修改时间"
              width="100px"
              align="center"
            />
            <el-table-column label="状态" width="80" align="center">
              <template #default="scope">
                {{ findKeyByValue(scope.row.status, klgAuditTypeDict) }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="270"
              align="center"
              v-if="curHeaderMode === 0 && canEdit"
            >
              <template #default="scope">
                <span class="operation">
                  <el-button class="op-btn" type="primary" @click="handleCopy(scope.row)" text>
                    复制
                  </el-button>
                  <el-button
                    class="op-btn"
                    type="primary"
                    style=""
                    :disabled="
                      !(
                        scope.row.status === klgAuditType.draft ||
                        scope.row.status === klgAuditType.returned ||
                        scope.row.status === klgAuditType.withdrawn
                      )
                    "
                    @click="handleSubmit(scope.row)"
                    text
                  >
                    提交
                  </el-button>
                  <el-button
                    class="op-btn"
                    type="primary"
                    v-if="
                      scope.row.status === klgAuditType.draft ||
                      scope.row.status === klgAuditType.returned ||
                      scope.row.status === klgAuditType.withdrawn
                    "
                    @click="handleEdit(scope.row)"
                    text
                  >
                    编辑
                  </el-button>
                  <el-button
                    class="op-btn"
                    type="primary"
                    v-else
                    @click="handleCheck(scope.row)"
                    text
                  >
                    查看
                  </el-button>
                  <el-button
                    class="op-btn"
                    type="primary"
                    :disabled="
                      scope.row.status === klgAuditType.pending ||
                      scope.row.status === klgAuditType.reviewing
                    "
                    @click="handleLink(scope.row)"
                    text
                  >
                    关联
                  </el-button>
                  <el-button
                    class="op-btn"
                    type="primary"
                    v-if="
                      scope.row.status != klgAuditType.pending &&
                      scope.row.status != klgAuditType.published &&
                      scope.row.status != klgAuditType.reviewing
                    "
                    @click="handleDelete(scope.row)"
                    text
                  >
                    删除
                  </el-button>
                  <el-button
                    class="op-btn"
                    type="primary"
                    v-else
                    @click="handleReturn(scope.row)"
                    :disabled="scope.row.status === klgAuditType.reviewing"
                    text
                  >
                    撤回
                  </el-button>
                </span>
              </template>
            </el-table-column>
          </el-table>
          <my-flipper
            @change-page="handleChangePage"
            :current="currentPage"
            :page-size="pageSize"
            :total="total"
          ></my-flipper>
        </div>
      </div>
    </div>
  </div>
  <!-- other -->
  <MulOpDialog ref="dialogRef" @op="handleRefreshList"></MulOpDialog>
</template>
<style scoped lang="less">
.clickable-title {
  cursor: pointer;
}

.background-image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('/static/klg-glass.png');
  background-size: 100%;
  user-select: none;
  .nopermission-tip {
    position: absolute;
    top: 300px;
    border: 1px solid var(--color-boxborder);
    background-color: white;
    padding: 10px;
    border-radius: 3px;
    font-size: 18px;
    font-weight: 800;
    color: var(--color-black);
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.wrapper {
  font-family: var(--text-family);
  width: 62.5vw;
  background-color: white;
  min-height: 750px;
  padding-bottom: 10px;

  .header-wrapper {
    width: 100%;
    .header-title {
      display: flex;
      font-weight: 600;
      padding: 10px 30px;
      min-height: 42px;
      color: var(--color-black);
    }
    .header-switcher {
      margin-top: 5px;
      width: 100%;
      display: flex;
      justify-content: center;
    }
    .header-searcher {
      .header-collapse {
        margin: 0 20px;
        :deep(.el-collapse-item__header) {
          background-color: var(--color-second);
          border-bottom: none;
        }
        :deep(.el-collapse-item__content) {
          border-bottom: 0;
          padding-bottom: 0;
        }
        :deep(.el-collapse-item__wrap) {
          background-color: var(--color-second);
        }
        :deep(svg) {
          color: white;
        }
      }
      .search-btn {
        font-family: var(--text-family);
        display: flex;
        background-color: var(--color-second);
        padding: 0 20px;
        margin: 0 20px;
        color: var(--color-black);
        font-size: 14px;
        text-align: center;
      }
      .search-bar {
        background-color: var(--color-second);
      }
    }
    .toolbar {
      margin: 7px 0;
      padding: 0 20px;
    }
  }
  .main-wrapper {
    padding: 0 10px;

    .table {
      --el-color-primary: var(--color-primary);
      :deep(p) {
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .cnt-card {
        padding: 10px 20px;
        display: flex;
        flex-direction: column;
        .expand-label {
          border: 1px solid var(--color-boxborder);
          border-radius: 5px;
          padding: 2px 10px;
          background-color: #ecf5ff;
          width: max-content;
          white-space: nowrap;
        }
        .proof-block-list {
          margin-top: 10px;
          padding: 10px;
          color: var(--color-black);
          background-color: var(--color-light);
          .proof-cond-list {
            display: flex;
            justify-content: row;
          }
          .proof-conclusion {
            display: flex;
            justify-content: row;
          }
        }
        .proof-label {
          font-weight: 600;
          margin-right: 10px;
        }
        .content {
          margin-top: 15px;
        }
      }

      .operation {
        padding: 0 10px;
        display: flex;

        .op-btn {
          padding: 5px;
          /* cursor: pointer; */
          font-family: var(--text-family);
          font-weight: 400;
          &:hover {
            font-weight: bold;
          }
        }
      }
    }
  }
}
:deep(.el-button--primary.is-text.is-disabled) {
  color: var(--color-boxborder);
}
:deep(.el-table .cell) {
  padding: 0 6px;
}
.search-bar {
  background-color: var(--color-second) !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

</style>
